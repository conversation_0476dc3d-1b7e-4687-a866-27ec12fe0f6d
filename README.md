# Luc System Vue

基于 Vite + Vue3 + TypeScript + Element Plus 的管理系统，从 vben-ele 项目迁移而来，保留了核心功能并简化了架构。

## 功能特性

- ✅ **现代技术栈**: Vite + Vue3 + TypeScript + Element Plus
- ✅ **动态路由**: 基于角色的路由权限控制
- ✅ **权限管理**: 菜单权限、按钮权限、角色权限
- ✅ **状态管理**: Pinia + 持久化存储
- ✅ **OAuth2 认证**: 支持标准 OAuth2 授权码流程
- ✅ **响应式布局**: 支持移动端适配
- ✅ **主题切换**: 支持明暗主题切换
- ✅ **国际化**: 多语言支持
- ✅ **TypeScript**: 完整的类型支持

## 项目结构

```text
luc-system-vue/
├── src/
│   ├── api/                 # API 接口
│   │   ├── auth.ts         # 认证相关接口
│   │   ├── menu.ts         # 菜单相关接口
│   │   └── request.ts      # 请求封装
│   ├── components/         # 公共组件
│   │   └── MenuItem.vue    # 菜单项组件
│   ├── constants/          # 常量定义
│   │   └── index.ts        # 应用常量
│   ├── layouts/            # 布局组件
│   │   ├── BasicLayout.vue # 基础布局
│   │   └── AuthLayout.vue  # 认证布局
│   ├── router/             # 路由配置
│   │   ├── routes/         # 路由定义
│   │   ├── guard.ts        # 路由守卫
│   │   └── access.ts       # 权限生成
│   ├── store/              # 状态管理
│   │   ├── user.ts         # 用户状态
│   │   ├── auth.ts         # 认证状态
│   │   ├── access.ts       # 权限状态
│   │   └── app.ts          # 应用状态
│   ├── types/              # 类型定义
│   │   └── global.d.ts     # 全局类型
│   ├── utils/              # 工具函数
│   │   ├── auth.ts         # 权限工具
│   │   ├── route.ts        # 路由工具
│   │   └── storage.ts      # 存储工具
│   ├── views/              # 页面组件
│   │   ├── auth/           # 认证页面
│   │   ├── dashboard/      # 仪表板
│   │   ├── error/          # 错误页面
│   │   └── system/         # 系统管理
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── public/                 # 静态资源
├── types/                  # 全局类型定义
├── .env                    # 环境变量
├── .env.development        # 开发环境变量
├── .env.production         # 生产环境变量
├── index.html              # HTML 模板
├── package.json            # 项目配置
├── tsconfig.json           # TypeScript 配置
└── vite.config.ts          # Vite 配置
```

## 快速开始

### 1. 安装依赖

```bash
cd luc-system-vue
npm install
# 或
yarn install
# 或
pnpm install
```

### 2. 配置环境变量

复制并修改环境变量文件：

```bash
cp .env.example .env.development
```

修改 `.env.development` 中的配置：

```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:8080

# OAuth2 配置
VITE_OAUTH_CLIENT_ID=luc-client
VITE_OAUTH_CLIENT_SECRET=luc-secret
```

### 3. 启动开发服务器

```bash
npm run dev
```

访问 <http://localhost:5173>

### 4. 构建生产版本

```bash
npm run build
```

## 核心功能说明

### 认证流程

1. **用户名密码登录** → 获取会话令牌
2. **OAuth2 授权** → 跳转到授权服务器
3. **授权码回调** → 交换访问令牌
4. **获取用户信息** → 设置权限和菜单

### 权限控制

- **路由权限**: 基于角色和权限码控制路由访问
- **菜单权限**: 动态生成用户可访问的菜单
- **按钮权限**: 基于权限码控制按钮显示

### 状态管理

- **用户状态**: 用户信息、令牌管理
- **权限状态**: 权限码、菜单、路由管理
- **应用状态**: 主题、布局、偏好设置
- **认证状态**: 登录流程、OAuth2 处理

## 与原项目的差异

### 简化的架构

- 移除了 monorepo 结构
- 去除了 mock 数据和 husky
- 简化了构建配置
- 减少了依赖复杂度

### 保留的核心功能

- 动态路由和权限控制
- OAuth2 认证流程
- 状态管理和持久化
- 响应式布局
- 主题切换

### 技术栈对比

| 功能 | 原项目 (vben-ele) | 迁移后 (luc-system-vue) |
|------|------------------|------------------------|
| 构建工具 | Vite | Vite |
| 框架 | Vue 3 | Vue 3 |
| 语言 | TypeScript | TypeScript |
| UI 库 | Element Plus | Element Plus |
| 状态管理 | Pinia | Pinia |
| 路由 | Vue Router | Vue Router |
| 请求库 | 自定义封装 | Axios + 自定义封装 |
| 项目结构 | Monorepo | 单体项目 |

## 开发指南

### 添加新页面

1. 在 `src/views/` 下创建页面组件
2. 在 `src/router/routes/dynamic.ts` 中添加路由配置
3. 设置相应的权限和角色

### 添加新 API

1. 在 `src/api/` 下创建 API 文件
2. 定义接口类型和请求函数
3. 在组件中使用

### 权限配置

```typescript
// 路由权限
{
  path: '/admin',
  meta: {
    roles: ['admin'],           // 角色权限
    permissions: ['admin:view'] // 权限码
  }
}

// 组件中检查权限
const userStore = useUserStore()
const hasPermission = userStore.hasPermission('admin:view')
```

## 部署说明

### 环境变量配置

生产环境需要配置：

```env
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_OAUTH_CLIENT_ID=your-client-id
VITE_OAUTH_CLIENT_SECRET=your-client-secret
```

### 构建和部署

```bash
# 构建
npm run build

# 部署到静态服务器
# 将 dist/ 目录内容部署到 Web 服务器
```

## 许可证

MIT License
