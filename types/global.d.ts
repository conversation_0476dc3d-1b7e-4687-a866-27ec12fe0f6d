// 通用类型
export type Recordable<T = any> = Record<string, T>

// 用户信息类型
export interface UserInfo {
  id: string
  username: string
  realName: string
  avatar?: string
  email?: string
  phone?: string
  roles: string[]
  permissions: string[]
  homePath?: string
}

// 菜单类型
export interface MenuInfo {
  id: string
  name: string
  path: string
  component?: string
  icon?: string
  title: string
  hidden?: boolean
  keepAlive?: boolean
  children?: MenuInfo[]
  meta?: {
    title: string
    icon?: string
    hidden?: boolean
    keepAlive?: boolean
    permissions?: string[]
  }
}

// 路由元信息
export interface RouteMeta {
  title: string
  icon?: string
  hidden?: boolean
  keepAlive?: boolean
  permissions?: string[]
  roles?: string[]
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// 全局类型声明
declare global {
  interface Window {
    // 全局变量
  }
}
