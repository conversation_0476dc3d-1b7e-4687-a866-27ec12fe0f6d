/**
 * 菜单相关 API
 */

import type { RouteRecordRaw } from 'vue-router'
import type { MenuInfo } from '@/types/global'
import { requestClient } from './request'

/**
 * 获取用户所有菜单
 */
export function getAllMenusApi(): Promise<MenuInfo[]> {
  // 暂时返回模拟数据，实际项目中应该调用后端接口
  return Promise.resolve([
    {
      id: '1',
      parentId: '',
      title: '仪表板',
      name: 'Dashboard',
      path: '/dashboard',
      component: 'dashboard/index',
      icon: 'dashboard',
      type: 1,
      sort: 1,
      hidden: false,
      permissions: ['dashboard:view'],
      roles: [],
      children: [],
    },
    {
      id: '2',
      parentId: '',
      title: '分析页',
      name: 'Analytics',
      path: '/dashboard/analytics',
      component: 'dashboard/analytics',
      icon: 'chart',
      type: 1,
      sort: 2,
      hidden: false,
      permissions: ['dashboard:analytics'],
      roles: [],
      children: [],
    },
    {
      id: '3',
      parentId: '',
      title: '系统管理',
      name: 'System',
      path: '/system',
      component: '',
      icon: 'system',
      type: 1,
      sort: 100,
      hidden: false,
      permissions: ['system:view'],
      roles: ['admin'],
      children: [],
    },
    {
      id: '31',
      parentId: '3',
      title: '用户管理',
      name: 'SystemUser',
      path: '/system/user',
      component: 'system/user/index',
      icon: 'user',
      type: 1,
      sort: 1,
      hidden: false,
      permissions: ['system:user:list'],
      roles: [],
      children: [],
    },
    {
      id: '32',
      parentId: '3',
      title: '角色管理',
      name: 'SystemRole',
      path: '/system/role',
      component: 'system/role/index',
      icon: 'role',
      type: 1,
      sort: 2,
      hidden: false,
      permissions: ['system:role:list'],
      roles: [],
      children: [],
    },
    {
      id: '33',
      parentId: '3',
      title: '菜单管理',
      name: 'SystemMenu',
      path: '/system/menu',
      component: 'system/menu/index',
      icon: 'menu',
      type: 1,
      sort: 3,
      hidden: false,
      permissions: ['system:menu:list'],
      roles: [],
      children: [],
    },
    {
      id: '34',
      parentId: '3',
      title: '部门管理',
      name: 'SystemDept',
      path: '/system/dept',
      component: 'system/dept/index',
      icon: 'dept',
      type: 1,
      sort: 4,
      hidden: false,
      permissions: ['system:dept:list'],
      roles: [],
      children: [],
    },
    {
      id: '4',
      parentId: '',
      title: '个人中心',
      name: 'Profile',
      path: '/profile',
      component: 'profile/index',
      icon: 'user',
      type: 1,
      sort: 999,
      hidden: true,
      permissions: [],
      roles: [],
      children: [],
    },
  ])

  // 实际项目中使用这行代码：
  // return requestClient.get<MenuInfo[]>('/menu/all')
}

/**
 * 获取用户菜单树
 */
export function getMenuTreeApi() {
  return requestClient.get<MenuInfo[]>('/menu/tree')
}

/**
 * 根据角色获取菜单
 */
export function getMenusByRoleApi(roleId: string) {
  return requestClient.get<MenuInfo[]>(`/menu/role/${roleId}`)
}
