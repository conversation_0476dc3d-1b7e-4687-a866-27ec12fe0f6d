/**
 * 认证相关 API
 */

import type { UserInfo } from '@/types/global'
import { requestClient, baseRequestClient } from './request'

// 登录参数
export interface LoginParams {
  username: string
  password: string
  captcha?: boolean
}

// 登录结果
export interface LoginResult {
  accessToken: string
  refreshToken?: string
}

// OAuth2 访问令牌参数
export interface OAuth2AccessTokenParams {
  grant_type: string
  code: string
  redirect_uri: string
  client_id: string
  client_secret: string
}

// OAuth2 访问令牌结果
export interface OAuth2AccessTokenResult {
  access_token: string
  expires_in: number
  token_type: string
  scope: string
  refresh_token: string
}

// 短信登录结果
export interface SmsLoginResult {
  accessToken: string
}

/**
 * 用户名密码登录
 */
export function loginApi(data: LoginParams) {
  return requestClient.post<string>('/login', data, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Accept: 'application/json',
    },
    responseReturn: 'body',
    withAuthorization: false,
  })
}

/**
 * 获取授权码
 */
export function getAuthorizationCodeApi(params: {
  client_id?: string
  redirectUrl: string
  state?: string
}) {
  const { redirectUrl, client_id = 'luc-client', state } = params
  return requestClient.get<string>('/oauth2/authorize', {
    params: {
      response_type: 'code',
      client_id,
      redirectUrl,
      state,
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    withAuthorization: false,
  })
}

/**
 * 使用授权码获取访问令牌
 */
export function getAccessTokenApi(params: OAuth2AccessTokenParams) {
  const basic = btoa(`${params.client_id}:${params.client_secret}`)
  return requestClient.post<OAuth2AccessTokenResult>('/oauth2/token', params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: `Basic ${basic}`,
      Accept: 'application/json',
    },
    responseReturn: 'body',
    withAuthorization: false,
  })
}

/**
 * 刷新访问令牌
 */
export function refreshTokenApi() {
  return baseRequestClient.post<{ data: string }>('/auth-server/refresh', null, {
    withCredentials: true,
  })
}

/**
 * 退出登录
 */
export function logoutApi() {
  return baseRequestClient.post('/logout', null, {
    withCredentials: true,
  })
}

/**
 * 获取用户信息
 */
export function getUserInfoApi() {
  return requestClient.get<UserInfo>('/user/info')
}

/**
 * 获取用户权限码
 */
export function getAccessCodesApi() {
  return requestClient.get<string[]>('/codes')
}

/**
 * 发送短信验证码
 */
export function sendSmsCodeApi(phone: string) {
  return requestClient.post('/sms/code', { phone }, {
    withAuthorization: false,
  })
}

/**
 * 短信验证码登录
 */
export function smsLoginApi(phoneNumber: string, code: string) {
  return requestClient.post<SmsLoginResult>('/sms/login', {
    phoneNumber,
    code,
  }, {
    withAuthorization: false,
  })
}
