/**
 * HTTP 请求客户端
 */

import type { AxiosInstance, AxiosRequestConfig } from 'axios'
import type { ApiResponse } from '@/types/global'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { useAuthStore } from '@/store/auth'
import { STORAGE_KEYS } from '@/constants'

// 请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  /**
   * 是否携带认证头
   */
  withAuthorization?: false | true
  /** 响应数据的返回方式。
   * raw: 原始的AxiosResponse，包括headers、status等，不做是否成功请求的检查。
   * body: 返回响应数据的BODY部分（只会根据status检查请求是否成功，忽略对code的判断，这种情况下应由调用方检查请求是否成功）。
   * data: 解构响应的BODY数据，只返回其中的data节点数据（会检查status和code是否为成功状态）。
   */
  responseReturn?: 'raw' | 'data' | 'body'
}

// 获取 CSRF Token 的函数
  function getCsrfToken(): null | string {
    // 从 cookie 获取
    const cookieMatch = document.cookie.match(/XSRF-TOKEN=([^;]+)/);
    return cookieMatch !== null && cookieMatch[1] !== undefined
      ? decodeURIComponent(cookieMatch[1])
      : null;
  }

function formatToken(token: null | string) {
  return token ? `Bearer ${token}` : null;
}

// 创建请求客户端类
class RequestClient {
  private instance: AxiosInstance
  private isRefreshing = false
  private refreshTokenQueue: ((token: string) => void)[] = []

  constructor(config: AxiosRequestConfig = {}) {
    // 创建 axios 实例
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json;charset=utf-8',
      },
      ...config,
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const requestConfig = config as RequestConfig
        // 添加认证头
        // 仅在未显式禁用认证且未设置 Authorization 时才注入 Bearer
        if (requestConfig.withAuthorization !== false && !requestConfig.headers?.Authorization) {
          const bearer = formatToken(this.getAccessToken());
          if (bearer) {
            config.headers.Authorization = bearer;
          }
        }

        // 添加语言头
        config.headers['Accept-Language'] = 'zh-CN'

        // 添加会话令牌
        const sessionToken = sessionStorage.getItem(STORAGE_KEYS.SESSION_TOKEN)
        if (sessionToken) {
          config.headers['luc-auth-token'] = sessionToken
        }

        // 添加 CSRF token
        const csrfToken = getCsrfToken()
        if (csrfToken) {
          config.headers['X-XSRF-TOKEN'] = csrfToken
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        const config = response.config as RequestConfig
        
        // 根据配置返回不同格式的数据
        if (config.responseReturn === 'raw') {
          return response
        } else if (config.responseReturn === 'body') {
          return response.data
        } else {
          // 默认返回 data 字段
          const data = response.data as ApiResponse
          if (data.success || data.code === 200) {
            return data.data
          } else {
            throw new Error(data.message || '请求失败')
          }
        }
      },
      async (error) => {
        const { response } = error
        
        if (response?.status === 401) {
          // Token 过期，尝试刷新
          return this.handleTokenExpired(error)
        }
        
        // 处理其他错误
        this.handleError(error)
        return Promise.reject(error)
      }
    )
  }

  private async handleTokenExpired(error: any) {
    const originalRequest = error.config

    if (!this.isRefreshing) {
      this.isRefreshing = true
      
      try {
        const newToken = await this.refreshToken()
        this.isRefreshing = false
        
        // 处理队列中的请求
        this.refreshTokenQueue.forEach(callback => callback(newToken))
        this.refreshTokenQueue = []
        
        // 重试原始请求
        originalRequest.headers.Authorization = `Bearer ${newToken}`
        return this.instance(originalRequest)
      } catch (refreshError) {
        this.isRefreshing = false
        this.refreshTokenQueue = []
        
        // 刷新失败，跳转到登录页
        const authStore = useAuthStore()
        await authStore.logout()
        
        return Promise.reject(refreshError)
      }
    } else {
      // 如果正在刷新，将请求加入队列
      return new Promise((resolve) => {
        this.refreshTokenQueue.push((token: string) => {
          originalRequest.headers.Authorization = `Bearer ${token}`
          resolve(this.instance(originalRequest))
        })
      })
    }
  }

  private async refreshToken(): Promise<string> {
    // 这里应该调用刷新 token 的 API
    // 暂时抛出错误，实际项目中需要实现
    throw new Error('Token refresh not implemented')
  }

  private getAccessToken(): string | null {
    const userStore = useUserStore()
    return userStore.accessToken
  }

  private handleError(error: any) {
    const { response } = error
    let message = '请求失败'

    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 400:
          message = data?.message || '请求参数错误'
          break
        case 403:
          message = '没有权限访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.code === 'NETWORK_ERROR') {
      message = '网络连接失败'
    } else if (error.code === 'TIMEOUT') {
      message = '请求超时'
    }

    ElMessage.error(message)
  }

  // HTTP 方法
  public get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  public post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  public put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  public delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }
}

// 创建请求实例
export const requestClient = new RequestClient({
  responseReturn: 'data',
} as RequestConfig)

export const baseRequestClient = new RequestClient({
  responseReturn: 'raw',
} as RequestConfig)
