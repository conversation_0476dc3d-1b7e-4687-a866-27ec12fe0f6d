/**
 * 认证状态管理
 */

import type { UserInfo } from '@/types/global'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElNotification } from 'element-plus'
import {
  loginApi,
  getAccessToken<PERSON>pi,
  getUserInfo<PERSON>pi,
  getAccessC<PERSON><PERSON>pi,
  logout<PERSON>pi
} from '@/api'
import type { LoginParams } from '@/api/auth'
import { useUserStore } from './user'
import { useAccessStore } from './access'
import { LOGIN_PATH, STORAGE_KEYS } from '@/constants'

// OAuth state key
const PKCE_STATE_KEY = 'pkce_state'

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()
  const userStore = useUserStore()
  const accessStore = useAccessStore()
  
  // 状态
  const loginLoading = ref(false)
  const { apiBase, base, clientId, clientSecret } = getAppConfig()

  // 获取应用配置
  function getAppConfig() {
    return {
      apiBase: import.meta.env.VITE_API_BASE_URL || '/api',
      base: import.meta.env.VITE_BASE || '/',
      clientId: import.meta.env.VITE_OAUTH_CLIENT_ID || 'luc-client',
      clientSecret: import.meta.env.VITE_OAUTH_CLIENT_SECRET || 'luc-secret',
      sessionKey: STORAGE_KEYS.SESSION_TOKEN,
    }
  }

  /**
   * 用户名密码登录
   */
  async function authLogin(params: LoginParams) {
    console.warn('authLogin 被调用，参数:', params)
    
    try {
      loginLoading.value = true

      // 1. 表单登录，获取会话令牌
      const sessionToken = await loginApi(params)
      console.warn('1、获取到sessionToken：', sessionToken)
      
      if (sessionToken) {
        
        // 2. 构造 OAuth2 授权请求
        const redirectUrl = `${location.origin}${base}oauth2/callback`
        const state = Date.now().toString(36)
        const scope = import.meta.env.VITE_OAUTH_SCOPE || 'read'
        
        // 存储 session 凭证和 state
        sessionStorage.setItem(STORAGE_KEYS.SESSION_TOKEN, sessionToken)
        sessionStorage.setItem(PKCE_STATE_KEY, state)
        
        // 跳转到授权页面
        const authUrl = `${apiBase}/oauth2/authorize?response_type=code` +
          `&client_id=${encodeURIComponent(clientId)}` +
          `&redirect_uri=${encodeURIComponent(redirectUrl)}` +
          `&state=${encodeURIComponent(state)}` +
          `&scope=${encodeURIComponent(scope)}`
        
        window.location.replace(authUrl)
        return
      } else {
        throw new Error('登录失败，未获取到会话令牌')
      }
    } catch (error) {
      console.error('登录失败:', error)
      ElNotification({
        message: error instanceof Error ? error.message : '登录失败',
        title: '登录失败',
        type: 'error',
      })
    } finally {
      loginLoading.value = false
    }
  }

  /**
   * OAuth2 回调处理
   */
  async function handleOAuth2Callback(code: string, state: string) {
    try {
      console.log('开始处理OAuth2回调，code:', code, 'state:', state)

      // 1. 校验 state
      const expectedState = sessionStorage.getItem(PKCE_STATE_KEY)
      console.log('校验state，期望值:', expectedState, '实际值:', state)
      if (expectedState && expectedState !== state) {
        throw new Error('state 校验失败')
      }

      // 2. 使用授权码获取访问令牌
      const redirectUri = `${location.origin}${base}oauth2/callback`
      console.log('准备获取访问令牌，redirectUri:', redirectUri)

      const tokenResult = await getAccessTokenApi({
        code,
        redirect_uri: redirectUri,
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: 'authorization_code',
      })
      console.log('获取访问令牌成功:', tokenResult)

      // 3. 存储令牌
      userStore.setAccessToken(tokenResult.access_token)
      accessStore.setAccessToken(tokenResult.access_token)

      if (tokenResult.refresh_token) {
        userStore.setRefreshToken(tokenResult.refresh_token)
        accessStore.setRefreshToken(tokenResult.refresh_token)
      }
      console.log('令牌存储完成')

      // 4. 获取用户信息和权限
      console.log('开始获取用户信息和权限')
      const userInfo = getUserInfoApi()
      
      console.log('获取用户信息成功:', userInfo)

      userStore.setUserInfo(userInfo)
      accessStore.setAccessCodes(userInfo.permissions)
      console.log('用户信息和权限存储完成')

      // 5. 清理临时数据
      sessionStorage.removeItem(PKCE_STATE_KEY)
      sessionStorage.removeItem(STORAGE_KEYS.SESSION_TOKEN)
      console.log('临时数据清理完成')

      // 6. 跳转到目标页面
      const redirect = userInfo.homePath || '/dashboard'
      console.log('准备跳转到:', redirect)
      await router.replace(redirect)
      console.log('页面跳转完成')

      ElNotification({
        message: `欢迎回来，${userInfo.realName || userInfo.username}！`,
        title: '登录成功',
        type: 'success',
      })

      return { userInfo }
    } catch (error) {
      console.error('OAuth2 回调处理失败:', error)
      throw error
    }
  }

  /**
   * OAuth2 登录
   */
  async function oauth2Login(providerId: string) {
    try {
      loginLoading.value = true
      window.location.href = `${apiBase}/oauth2/authorization/${providerId}`
    } finally {
      loginLoading.value = false
    }
  }

  /**
   * 退出登录
   */
  async function logout(redirect: boolean = true) {
    try {
      await logoutApi()
    } catch {
      // 忽略退出登录的错误
    }

    // 清理所有状态
    userStore.clearUserInfo()
    accessStore.clearAccess()

    if (redirect) {
      await router.replace({
        path: LOGIN_PATH,
        query: {
          redirect: encodeURIComponent(router.currentRoute.value.fullPath),
        },
      })
    }
  }

  /**
   * 获取用户信息
   */
  async function fetchUserInfo() {
    try {
      const userInfo = await getUserInfoApi()
      userStore.setUserInfo(userInfo)
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 清除无效的 token
      userStore.setAccessToken(null)
      accessStore.setAccessToken(null)
      throw error
    }
  }

  return {
    // 状态
    loginLoading,
    
    // Actions
    authLogin,
    handleOAuth2Callback,
    oauth2Login,
    logout,
    fetchUserInfo,
  }
})
