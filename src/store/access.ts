/**
 * 权限访问状态管理
 */

import type { RouteRecordRaw } from 'vue-router'
import type { MenuInfo } from '@/types/global'
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { STORAGE_KEYS } from '@/constants'

export const useAccessStore = defineStore(
  'access',
  () => {
    // 状态
    const accessToken = ref<string | null>(null)
    const refreshToken = ref<string | null>(null)
    const accessCodes = ref<string[]>([])
    const accessMenus = ref<MenuInfo[]>([])
    const accessRoutes = ref<RouteRecordRaw[]>([])
    const isAccessChecked = ref(false)
    const loginExpired = ref(false)

    // Getters
    const isLoggedIn = computed(() => !!accessToken.value)
    const hasAccessCodes = computed(() => accessCodes.value.length > 0)
    const hasAccessMenus = computed(() => accessMenus.value.length > 0)

    // Actions
    function setAccessToken(token: string | null) {
      accessToken.value = token
    }

    function setRefreshToken(token: string | null) {
      refreshToken.value = token
    }

    function setAccessCodes(codes: string[]) {
      accessCodes.value = codes
    }

    function setAccessMenus(menus: MenuInfo[]) {
      accessMenus.value = menus
    }

    function setAccessRoutes(routes: RouteRecordRaw[]) {
      accessRoutes.value = routes
    }

    function setIsAccessChecked(checked: boolean) {
      isAccessChecked.value = checked
    }

    function setLoginExpired(expired: boolean) {
      loginExpired.value = expired
    }

    function hasPermission(permission: string | string[]): boolean {
      if (!accessCodes.value.length) return false
      
      if (typeof permission === 'string') {
        return accessCodes.value.includes(permission)
      }
      
      return permission.some(p => accessCodes.value.includes(p))
    }

    function getMenuByPath(path: string): MenuInfo | undefined {
      function findMenu(menus: MenuInfo[], targetPath: string): MenuInfo | undefined {
        for (const menu of menus) {
          if (menu.path === targetPath) {
            return menu
          }
          if (menu.children) {
            const found = findMenu(menu.children, targetPath)
            if (found) {
              return found
            }
          }
        }
        return undefined
      }
      
      return findMenu(accessMenus.value, path)
    }

    function clearAccess() {
      accessToken.value = null
      refreshToken.value = null
      accessCodes.value = []
      accessMenus.value = []
      accessRoutes.value = []
      isAccessChecked.value = false
      loginExpired.value = false
    }

    function reset() {
      clearAccess()
    }

    return {
      // 状态
      accessToken,
      refreshToken,
      accessCodes,
      accessMenus,
      accessRoutes,
      isAccessChecked,
      loginExpired,
      
      // Getters
      isLoggedIn,
      hasAccessCodes,
      hasAccessMenus,
      
      // Actions
      setAccessToken,
      setRefreshToken,
      setAccessCodes,
      setAccessMenus,
      setAccessRoutes,
      setIsAccessChecked,
      setLoginExpired,
      hasPermission,
      getMenuByPath,
      clearAccess,
      reset,
    }
  },
  {
    persist: {
      key: STORAGE_KEYS.PERMISSIONS,
      storage: localStorage,
      paths: ['accessToken', 'refreshToken', 'accessCodes'],
    },
  }
)
