/**
 * 用户信息状态管理
 */

import type { UserInfo } from '@/types/global'
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { STORAGE_KEYS } from '@/constants'

export const useUserStore = defineStore(
  'user',
  () => {
    // 状态
    const userInfo = ref<UserInfo | null>(null)
    const accessToken = ref<string | null>(null)
    const refreshToken = ref<string | null>(null)

    // Getters
    const isLoggedIn = computed(() => !!accessToken.value)
    const userId = computed(() => userInfo.value?.id)
    const username = computed(() => userInfo.value?.username)
    const realName = computed(() => userInfo.value?.realName)
    const avatar = computed(() => userInfo.value?.avatar)
    const roles = computed(() => userInfo.value?.roles || [])
    const permissions = computed(() => userInfo.value?.permissions || [])

    // Actions
    function setUserInfo(info: UserInfo | null) {
      userInfo.value = info
    }

    function setAccessToken(token: string | null) {
      accessToken.value = token
    }

    function setRefreshToken(token: string | null) {
      refreshToken.value = token
    }

    function updateUserInfo(updates: Partial<UserInfo>) {
      if (userInfo.value) {
        Object.assign(userInfo.value, updates)
      }
    }

    function clearUserInfo() {
      userInfo.value = null
      accessToken.value = null
      refreshToken.value = null
    }

    function hasRole(role: string | string[]): boolean {
      const userRoles = roles.value
      if (!userRoles.length) return false
      
      if (typeof role === 'string') {
        return userRoles.includes(role)
      }
      
      return role.some(r => userRoles.includes(r))
    }

    function hasPermission(permission: string | string[]): boolean {
      const userPermissions = permissions.value
      if (!userPermissions.length) return false
      
      if (typeof permission === 'string') {
        return userPermissions.includes(permission)
      }
      
      return permission.some(p => userPermissions.includes(p))
    }

    return {
      // 状态
      userInfo,
      accessToken,
      refreshToken,
      
      // Getters
      isLoggedIn,
      userId,
      username,
      realName,
      avatar,
      roles,
      permissions,
      
      // Actions
      setUserInfo,
      setAccessToken,
      setRefreshToken,
      updateUserInfo,
      clearUserInfo,
      hasRole,
      hasPermission,
    }
  },
  {
    persist: {
      key: STORAGE_KEYS.USER_INFO,
      storage: localStorage,
      paths: ['userInfo', 'accessToken', 'refreshToken'],
    },
  }
)
