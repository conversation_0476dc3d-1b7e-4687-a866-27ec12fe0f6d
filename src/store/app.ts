/**
 * 应用状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { STORAGE_KEYS, THEME_MODES, LAYOUT_MODES } from '@/constants'

export interface AppPreferences {
  // 主题设置
  theme: {
    mode: 'light' | 'dark' | 'auto'
    colorPrimary: string
    colorSuccess: string
    colorWarning: string
    colorError: string
  }
  
  // 布局设置
  layout: {
    mode: 'sidebar' | 'top-menu' | 'mix'
    sidebarCollapsed: boolean
    sidebarWidth: number
    headerHeight: number
    showBreadcrumb: boolean
    showTabbar: boolean
  }
  
  // 应用设置
  app: {
    title: string
    locale: string
    enableWatermark: boolean
    enableProgress: boolean
    enableKeepAlive: boolean
  }
}

const defaultPreferences: AppPreferences = {
  theme: {
    mode: 'light',
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
  },
  layout: {
    mode: 'sidebar',
    sidebarCollapsed: false,
    sidebarWidth: 240,
    headerHeight: 64,
    showBreadcrumb: true,
    showTabbar: true,
  },
  app: {
    title: 'Luc Admin',
    locale: 'zh-CN',
    enableWatermark: false,
    enableProgress: true,
    enableKeepAlive: true,
  },
}

export const useAppStore = defineStore(
  'app',
  () => {
    // 状态
    const preferences = ref<AppPreferences>({ ...defaultPreferences })
    const loading = ref(false)
    const collapsed = ref(false)

    // Getters
    const isDark = computed(() => {
      if (preferences.value.theme.mode === 'auto') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches
      }
      return preferences.value.theme.mode === 'dark'
    })

    const isMobile = computed(() => {
      return window.innerWidth < 768
    })

    const sidebarCollapsed = computed({
      get: () => collapsed.value || (isMobile.value && !collapsed.value),
      set: (value: boolean) => {
        collapsed.value = value
      },
    })

    // Actions
    function setLoading(value: boolean) {
      loading.value = value
    }

    function toggleSidebar() {
      collapsed.value = !collapsed.value
    }

    function setSidebarCollapsed(value: boolean) {
      collapsed.value = value
    }

    function updatePreferences(updates: Partial<AppPreferences>) {
      Object.assign(preferences.value, updates)
    }

    function setTheme(theme: Partial<AppPreferences['theme']>) {
      Object.assign(preferences.value.theme, theme)
      applyTheme()
    }

    function setLayout(layout: Partial<AppPreferences['layout']>) {
      Object.assign(preferences.value.layout, layout)
    }

    function setLocale(locale: string) {
      preferences.value.app.locale = locale
    }

    function resetPreferences() {
      preferences.value = { ...defaultPreferences }
    }

    // 应用主题
    function applyTheme() {
      const { theme } = preferences.value
      const root = document.documentElement
      
      // 设置主题模式
      if (isDark.value) {
        root.classList.add('dark')
        root.classList.remove('light')
      } else {
        root.classList.add('light')
        root.classList.remove('dark')
      }
      
      // 设置主题色
      root.style.setProperty('--color-primary', theme.colorPrimary)
      root.style.setProperty('--color-success', theme.colorSuccess)
      root.style.setProperty('--color-warning', theme.colorWarning)
      root.style.setProperty('--color-error', theme.colorError)
    }

    // 初始化
    function init() {
      applyTheme()
      
      // 监听系统主题变化
      if (preferences.value.theme.mode === 'auto') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', applyTheme)
      }
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (isMobile.value) {
          setSidebarCollapsed(true)
        }
      })
    }

    return {
      // 状态
      preferences,
      loading,
      collapsed,
      
      // Getters
      isDark,
      isMobile,
      sidebarCollapsed,
      
      // Actions
      setLoading,
      toggleSidebar,
      setSidebarCollapsed,
      updatePreferences,
      setTheme,
      setLayout,
      setLocale,
      resetPreferences,
      applyTheme,
      init,
    }
  },
  {
    persist: {
      key: STORAGE_KEYS.PREFERENCES,
      storage: localStorage,
      paths: ['preferences', 'collapsed'],
    },
  }
)
