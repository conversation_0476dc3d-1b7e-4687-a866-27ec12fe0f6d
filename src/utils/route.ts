/**
 * 路由相关工具函数
 */

import type { RouteRecordRaw, Router } from 'vue-router'
import type { MenuInfo } from '@/types/global'
import { filterTree, mapTree } from './tree'
import { hasAuthority } from './auth'

/**
 * 动态生成路由 - 前端方式
 */
export async function generateRoutesByFrontend(
  routes: RouteRecordRaw[],
  roles: string[],
  forbiddenComponent?: RouteRecordRaw['component']
): Promise<RouteRecordRaw[]> {
  // 根据角色标识过滤路由表
  const finalRoutes = filterTree(routes, (route) => {
    return hasAuthority(route, roles)
  })

  if (!forbiddenComponent) {
    return finalRoutes
  }

  // 如果有禁止访问的页面，将禁止访问的页面替换为403页面
  return mapTree(finalRoutes, (route) => {
    if (menuHasVisibleWithForbidden(route)) {
      route.component = forbiddenComponent
    }
    return route
  })
}

/**
 * 根据路由生成菜单
 */
export async function generateMenus(
  routes: RouteRecordRaw[],
  router: Router
): Promise<MenuInfo[]> {
  // 获取所有路由的最终路径映射
  const finalRoutesMap: Record<string, string> = Object.fromEntries(
    router.getRoutes().map(({ name, path }) => [name, path])
  )

  // 转换路由为菜单
  const menus = mapTree(routes, (route): MenuInfo => {
    const meta = route.meta || {}
    return {
      id: route.name as string,
      name: route.name as string,
      path: finalRoutesMap[route.name as string] || route.path,
      component: route.component as string,
      icon: meta.icon,
      title: meta.title || route.name as string,
      hidden: meta.hidden || false,
      keepAlive: meta.keepAlive || false,
      meta: {
        title: meta.title || route.name as string,
        icon: meta.icon,
        hidden: meta.hidden,
        keepAlive: meta.keepAlive,
        permissions: meta.permissions,
      },
    }
  })

  // 过滤隐藏的菜单
  return filterTree(menus, (menu) => !menu.hidden)
}

/**
 * 合并路由模块
 */
export function mergeRouteModules(
  modules: Record<string, any>
): RouteRecordRaw[] {
  const routeModuleList: RouteRecordRaw[] = []

  Object.keys(modules).forEach((key) => {
    const mod = modules[key].default || {}
    const modList = Array.isArray(mod) ? [...mod] : [mod]
    routeModuleList.push(...modList)
  })

  return routeModuleList
}

/**
 * 重置路由
 */
export function resetRoutes(router: Router, routes: RouteRecordRaw[]): void {
  // 移除所有动态路由
  const routeNames = router.getRoutes().map(route => route.name)
  routeNames.forEach(name => {
    if (name && !isStaticRoute(name as string)) {
      router.removeRoute(name)
    }
  })

  // 重新添加路由
  routes.forEach(route => {
    router.addRoute(route)
  })
}

/**
 * 检查是否为静态路由
 */
function isStaticRoute(name: string): boolean {
  const staticRoutes = ['Root', 'Login', 'NotFound']
  return staticRoutes.includes(name)
}

/**
 * 检查菜单是否可见但被禁止访问
 */
function menuHasVisibleWithForbidden(route: RouteRecordRaw): boolean {
  const meta = route.meta
  return !!(meta && !meta.hidden && meta.permissions && meta.permissions.length > 0)
}

/**
 * 根据路径查找菜单
 */
export function findMenuByPath(
  menus: MenuInfo[],
  path: string
): MenuInfo | undefined {
  for (const menu of menus) {
    if (menu.path === path) {
      return menu
    }
    if (menu.children) {
      const found = findMenuByPath(menu.children, path)
      if (found) {
        return found
      }
    }
  }
  return undefined
}
