/**
 * 树形数据处理工具函数
 */

export interface TreeNode {
  children?: TreeNode[]
  [key: string]: any
}

/**
 * 过滤树形数据
 */
export function filterTree<T extends TreeNode>(
  tree: T[],
  predicate: (node: T) => boolean,
  childrenKey = 'children'
): T[] {
  return tree
    .map((node) => ({ ...node }))
    .filter((node) => {
      if (node[childrenKey]) {
        node[childrenKey] = filterTree(node[childrenKey], predicate, childrenKey)
      }
      return predicate(node) || (node[childrenKey] && node[childrenKey].length > 0)
    })
}

/**
 * 映射树形数据
 */
export function mapTree<T extends TreeNode, R extends TreeNode>(
  tree: T[],
  mapper: (node: T) => R,
  childrenKey = 'children'
): R[] {
  return tree.map((node) => {
    const mapped = mapper(node)
    if (node[childrenKey]) {
      mapped[childrenKey] = mapTree(node[childrenKey], mapper, childrenKey)
    }
    return mapped
  })
}

/**
 * 遍历树形数据
 */
export function traverseTree<T extends TreeNode>(
  tree: T[],
  callback: (node: T, parent?: T) => void,
  childrenKey = 'children',
  parent?: T
): void {
  tree.forEach((node) => {
    callback(node, parent)
    if (node[childrenKey]) {
      traverseTree(node[childrenKey], callback, childrenKey, node)
    }
  })
}

/**
 * 查找树形数据中的节点
 */
export function findInTree<T extends TreeNode>(
  tree: T[],
  predicate: (node: T) => boolean,
  childrenKey = 'children'
): T | undefined {
  for (const node of tree) {
    if (predicate(node)) {
      return node
    }
    if (node[childrenKey]) {
      const found = findInTree(node[childrenKey], predicate, childrenKey)
      if (found) {
        return found
      }
    }
  }
  return undefined
}

/**
 * 将树形数据转换为扁平数组
 */
export function flattenTree<T extends TreeNode>(
  tree: T[],
  childrenKey = 'children'
): T[] {
  const result: T[] = []
  
  function flatten(nodes: T[]) {
    nodes.forEach((node) => {
      result.push(node)
      if (node[childrenKey]) {
        flatten(node[childrenKey])
      }
    })
  }
  
  flatten(tree)
  return result
}
