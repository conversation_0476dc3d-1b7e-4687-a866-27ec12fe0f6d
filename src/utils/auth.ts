/**
 * 权限相关工具函数
 */

import type { RouteRecordRaw } from 'vue-router'

/**
 * 检查是否有权限
 */
export function hasAuthority(
  route: RouteRecordRaw,
  roles: string[]
): boolean {
  const routeRoles = route.meta?.roles
  
  if (!routeRoles || routeRoles.length === 0) {
    return true
  }
  
  return roles.some(role => routeRoles.includes(role))
}

/**
 * 检查是否有指定权限码
 */
export function hasPermission(
  permissions: string[],
  permission: string | string[]
): boolean {
  if (!permissions || permissions.length === 0) {
    return false
  }
  
  if (typeof permission === 'string') {
    return permissions.includes(permission)
  }
  
  return permission.some(p => permissions.includes(p))
}

/**
 * 检查是否有指定角色
 */
export function hasRole(
  roles: string[],
  role: string | string[]
): boolean {
  if (!roles || roles.length === 0) {
    return false
  }
  
  if (typeof role === 'string') {
    return roles.includes(role)
  }
  
  return role.some(r => roles.includes(r))
}

/**
 * 检查菜单是否可见但被禁止访问
 */
export function menuHasVisibleWithForbidden(route: RouteRecordRaw): boolean {
  const meta = route.meta
  return !!(meta && !meta.hidden && meta.permissions && meta.permissions.length > 0)
}
