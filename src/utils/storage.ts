/**
 * 本地存储工具类
 */

export interface StorageOptions {
  prefix?: string
  expire?: number
}

export class StorageManager {
  private prefix: string

  constructor(options: StorageOptions = {}) {
    this.prefix = options.prefix || 'luc_'
  }

  private getKey(key: string): string {
    return `${this.prefix}${key}`
  }

  /**
   * 设置存储项
   */
  setItem<T = any>(key: string, value: T, expire?: number): void {
    const storageKey = this.getKey(key)
    const data = {
      value,
      expire: expire ? Date.now() + expire * 1000 : null,
    }
    
    try {
      localStorage.setItem(storageKey, JSON.stringify(data))
    } catch (error) {
      console.error('Storage setItem error:', error)
    }
  }

  /**
   * 获取存储项
   */
  getItem<T = any>(key: string): T | null {
    const storageKey = this.getKey(key)
    
    try {
      const item = localStorage.getItem(storageKey)
      if (!item) return null

      const data = JSON.parse(item)
      
      // 检查是否过期
      if (data.expire && Date.now() > data.expire) {
        this.removeItem(key)
        return null
      }
      
      return data.value
    } catch (error) {
      console.error('Storage getItem error:', error)
      return null
    }
  }

  /**
   * 移除存储项
   */
  removeItem(key: string): void {
    const storageKey = this.getKey(key)
    localStorage.removeItem(storageKey)
  }

  /**
   * 清空所有存储项
   */
  clear(): void {
    const keys = Object.keys(localStorage)
    keys.forEach((key) => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key)
      }
    })
  }

  /**
   * 检查存储项是否存在
   */
  hasItem(key: string): boolean {
    return this.getItem(key) !== null
  }
}

// 默认存储实例
export const storage = new StorageManager()
