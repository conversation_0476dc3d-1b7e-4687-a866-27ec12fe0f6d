/**
 * 类型推断和判断工具函数
 */

/**
 * 判断是否为字符串
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string'
}

/**
 * 判断是否为数字
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !Number.isNaN(value)
}

/**
 * 判断是否为布尔值
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean'
}

/**
 * 判断是否为函数
 */
export function isFunction(value: unknown): value is Function {
  return typeof value === 'function'
}

/**
 * 判断是否为对象
 */
export function isObject(value: unknown): value is Record<string, any> {
  return value !== null && typeof value === 'object'
}

/**
 * 判断是否为数组
 */
export function isArray(value: unknown): value is any[] {
  return Array.isArray(value)
}

/**
 * 判断是否为 undefined
 */
export function isUndefined(value: unknown): value is undefined {
  return typeof value === 'undefined'
}

/**
 * 判断是否为 null
 */
export function isNull(value: unknown): value is null {
  return value === null
}

/**
 * 判断是否为空值（null 或 undefined）
 */
export function isEmpty(value: unknown): value is null | undefined {
  return value === null || value === undefined
}

/**
 * 判断是否为 HTTP URL
 */
export function isHttpUrl(value: string): boolean {
  return /^https?:\/\//.test(value)
}

/**
 * 判断是否为 Window 对象
 */
export function isWindow(value: unknown): value is Window {
  return typeof window !== 'undefined' && value === window
}

/**
 * 获取第一个非空值
 */
export function getFirstNonNullOrUndefined<T>(
  ...values: (null | T | undefined)[]
): T | undefined {
  for (const value of values) {
    if (value !== undefined && value !== null) {
      return value
    }
  }
  return undefined
}
