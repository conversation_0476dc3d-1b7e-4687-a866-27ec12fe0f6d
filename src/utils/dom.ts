/**
 * DOM 操作工具函数
 */

/**
 * 更新 CSS 变量
 */
export function updateCSSVariables(
  variables: Record<string, string>,
  id = '__luc-styles__'
): void {
  const styleElement =
    document.querySelector(`#${id}`) || document.createElement('style')

  styleElement.id = id

  let cssText = ':root {'
  for (const key in variables) {
    if (Object.prototype.hasOwnProperty.call(variables, key)) {
      cssText += `${key}: ${variables[key]};`
    }
  }
  cssText += '}'

  styleElement.textContent = cssText

  if (!document.querySelector(`#${id}`)) {
    setTimeout(() => {
      document.head.append(styleElement)
    })
  }
}

/**
 * 移除全局 loading
 */
export function unmountGlobalLoading() {
  const loadingElement = document.querySelector('#__app-loading__')

  if (loadingElement) {
    loadingElement.classList.add('hidden')

    const injectLoadingElements = document.querySelectorAll(
      '[data-app-loading^="inject"]'
    )

    loadingElement.addEventListener(
      'transitionend',
      () => {
        loadingElement.remove()
        injectLoadingElements.forEach((el) => el.remove())
      },
      { once: true }
    )
  }
}

/**
 * 获取弹窗容器
 */
export function getPopupContainer(triggerNode?: HTMLElement): HTMLElement {
  return triggerNode?.parentElement ?? document.body
}
