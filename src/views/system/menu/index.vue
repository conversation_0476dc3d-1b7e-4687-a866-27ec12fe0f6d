<template>
  <div class="menu-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>菜单管理</span>
          <el-button type="primary" @click="handleAdd">
            新增菜单
          </el-button>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="title" label="菜单名称" width="200">
          <template #default="{ row }">
            <el-icon v-if="row.icon" style="margin-right: 5px;">
              <component :is="row.icon" />
            </el-icon>
            {{ row.title }}
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路径" />
        <el-table-column prop="component" label="组件" />
        <el-table-column prop="permission" label="权限标识" />
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column prop="type" label="类型" width="80">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">
              {{ getTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="handleAddChild(row)">
              新增子菜单
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const tableData = ref([
  {
    id: 1,
    title: '仪表板',
    path: '/dashboard',
    component: 'dashboard/index',
    icon: 'Dashboard',
    permission: 'dashboard:view',
    sort: 1,
    type: 1, // 1: 菜单, 2: 按钮
    status: 1,
    children: [
      {
        id: 11,
        title: '工作台',
        path: '/dashboard/index',
        component: 'dashboard/index',
        permission: 'dashboard:index',
        sort: 1,
        type: 1,
        status: 1,
      },
      {
        id: 12,
        title: '分析页',
        path: '/dashboard/analytics',
        component: 'dashboard/analytics',
        permission: 'dashboard:analytics',
        sort: 2,
        type: 1,
        status: 1,
      },
    ],
  },
  {
    id: 2,
    title: '系统管理',
    path: '/system',
    component: '',
    icon: 'Setting',
    permission: 'system:view',
    sort: 2,
    type: 1,
    status: 1,
    children: [
      {
        id: 21,
        title: '用户管理',
        path: '/system/user',
        component: 'system/user/index',
        permission: 'system:user:list',
        sort: 1,
        type: 1,
        status: 1,
      },
      {
        id: 22,
        title: '角色管理',
        path: '/system/role',
        component: 'system/role/index',
        permission: 'system:role:list',
        sort: 2,
        type: 1,
        status: 1,
      },
      {
        id: 23,
        title: '菜单管理',
        path: '/system/menu',
        component: 'system/menu/index',
        permission: 'system:menu:list',
        sort: 3,
        type: 1,
        status: 1,
      },
    ],
  },
])

onMounted(() => {
  loadData()
})

function loadData() {
  // TODO: 调用 API 加载数据
}

function getTypeName(type: number) {
  const typeMap: Record<number, string> = {
    1: '菜单',
    2: '按钮',
  }
  return typeMap[type] || '未知'
}

function getTypeColor(type: number) {
  const colorMap: Record<number, string> = {
    1: 'primary',
    2: 'success',
  }
  return colorMap[type] || 'info'
}

function handleAdd() {
  // TODO: 打开新增菜单对话框
  ElMessage.info('新增菜单功能待实现')
}

function handleEdit(row: any) {
  // TODO: 打开编辑菜单对话框
  ElMessage.info(`编辑菜单: ${row.title}`)
}

function handleAddChild(row: any) {
  // TODO: 打开新增子菜单对话框
  ElMessage.info(`为 ${row.title} 新增子菜单`)
}

async function handleDelete(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单 ${row.title} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // TODO: 调用 API 删除菜单
    ElMessage.success('删除成功')
  } catch {
    // 用户取消操作
  }
}
</script>

<style scoped>
.menu-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
