<template>
  <div class="dept-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>部门管理</span>
          <el-button type="primary" @click="handleAdd">
            新增部门
          </el-button>
        </div>
      </template>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="部门名称" width="200" />
        <el-table-column prop="code" label="部门编码" />
        <el-table-column prop="leader" label="负责人" />
        <el-table-column prop="phone" label="联系电话" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="handleAddChild(row)">
              新增子部门
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const tableData = ref([
  {
    id: 1,
    name: '总公司',
    code: 'ROOT',
    leader: '张三',
    phone: '13800138000',
    email: '<EMAIL>',
    sort: 1,
    status: 1,
    createTime: '2024-01-01 10:00:00',
    children: [
      {
        id: 11,
        name: '技术部',
        code: 'TECH',
        leader: '李四',
        phone: '13800138001',
        email: '<EMAIL>',
        sort: 1,
        status: 1,
        createTime: '2024-01-02 10:00:00',
        children: [
          {
            id: 111,
            name: '前端组',
            code: 'FRONTEND',
            leader: '王五',
            phone: '13800138002',
            email: '<EMAIL>',
            sort: 1,
            status: 1,
            createTime: '2024-01-03 10:00:00',
          },
          {
            id: 112,
            name: '后端组',
            code: 'BACKEND',
            leader: '赵六',
            phone: '13800138003',
            email: '<EMAIL>',
            sort: 2,
            status: 1,
            createTime: '2024-01-03 10:00:00',
          },
        ],
      },
      {
        id: 12,
        name: '市场部',
        code: 'MARKET',
        leader: '钱七',
        phone: '13800138004',
        email: '<EMAIL>',
        sort: 2,
        status: 1,
        createTime: '2024-01-02 10:00:00',
      },
    ],
  },
])

onMounted(() => {
  loadData()
})

function loadData() {
  // TODO: 调用 API 加载数据
}

function handleAdd() {
  // TODO: 打开新增部门对话框
  ElMessage.info('新增部门功能待实现')
}

function handleEdit(row: any) {
  // TODO: 打开编辑部门对话框
  ElMessage.info(`编辑部门: ${row.name}`)
}

function handleAddChild(row: any) {
  // TODO: 打开新增子部门对话框
  ElMessage.info(`为 ${row.name} 新增子部门`)
}

async function handleDelete(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要删除部门 ${row.name} 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // TODO: 调用 API 删除部门
    ElMessage.success('删除成功')
  } catch {
    // 用户取消操作
  }
}
</script>

<style scoped>
.dept-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
