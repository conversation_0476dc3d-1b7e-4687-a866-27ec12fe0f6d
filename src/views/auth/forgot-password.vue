<template>
  <div class="forgot-password-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      size="large"
    >
      <el-form-item prop="email">
        <el-input
          v-model="form.email"
          placeholder="请输入邮箱"
          prefix-icon="Message"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button
          type="primary"
          :loading="loading"
          style="width: 100%"
          @click="handleSendEmail"
        >
          发送重置邮件
        </el-button>
      </el-form-item>
    </el-form>
    
    <div class="forgot-password-footer">
      <el-link type="primary" @click="$router.push('/auth/login')">
        返回登录
      </el-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(false)
const formRef = ref<FormInstance>()

const form = reactive({
  email: '',
})

const rules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
}

async function handleSendEmail() {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate()
  if (valid) {
    loading.value = true
    try {
      // TODO: 调用发送重置邮件 API
      ElMessage.success('重置邮件已发送，请查收邮箱')
      await new Promise(resolve => setTimeout(resolve, 1000))
      router.push('/auth/login')
    } catch (error) {
      ElMessage.error('发送失败，请重试')
    } finally {
      loading.value = false
    }
  }
}
</script>

<style scoped>
.forgot-password-form {
  width: 100%;
}

.forgot-password-footer {
  text-align: center;
  margin-top: 20px;
}
</style>
