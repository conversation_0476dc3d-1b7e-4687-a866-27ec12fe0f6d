<template>
  <div class="oauth2-callback">
    <div class="callback-container">
      <div v-if="loading" class="loading">
        <el-icon class="loading-icon">
          <Loading />
        </el-icon>
        <h2>OAuth2 登录处理中</h2>
        <p>{{ message }}</p>
      </div>

      <div v-else class="result">
        <el-icon v-if="success" class="success-icon">
          <SuccessFilled />
        </el-icon>
        <el-icon v-else class="error-icon">
          <CircleCloseFilled />
        </el-icon>
        
        <h2>{{ success ? '登录成功' : '登录失败' }}</h2>
        <p>{{ message }}</p>

        <div v-if="!success" class="actions">
          <el-button type="primary" @click="$router.push('/auth/login')">
            返回登录页面
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Loading, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { useAuthStore } from '@/store/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const loading = ref(true)
const success = ref(false)
const message = ref('正在处理OAuth2登录...')

onMounted(async () => {
  try {
    console.log('OAuth2回调页面加载，查询参数:', route.query)

    const code = route.query.code as string
    const state = route.query.state as string
    const error = route.query.error as string

    console.log('解析参数 - code:', code, 'state:', state, 'error:', error)

    if (error) {
      throw new Error(`OAuth2授权失败: ${error}`)
    }

    if (!code) {
      throw new Error('未找到授权码')
    }

    message.value = '正在使用授权码获取访问令牌...'
    console.log('开始处理OAuth2回调')

    // 处理 OAuth2 回调
    await authStore.handleOAuth2Callback(code, state)

    success.value = true
    message.value = '登录成功，正在跳转...'
    console.log('OAuth2回调处理成功')
  } catch (error) {
    console.error('OAuth2 回调处理失败:', error)
    success.value = false
    message.value = `登录失败: ${error instanceof Error ? error.message : '未知错误'}`

    // 3秒后跳转到登录页面
    setTimeout(() => {
      console.log('3秒后跳转到登录页面')
      router.replace('/auth/login?error=oauth2_callback_failed')
    }, 3000)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.oauth2-callback {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.callback-container {
  background: white;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.loading-icon {
  font-size: 48px;
  color: #409eff;
  animation: spin 1s linear infinite;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
}

.error-icon {
  font-size: 48px;
  color: #f56c6c;
}

.loading h2,
.result h2 {
  margin: 20px 0 10px;
  color: #333;
}

.loading p,
.result p {
  color: #666;
  margin-bottom: 20px;
}

.actions {
  margin-top: 20px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
