<template>
  <div class="login-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      size="large"
    >
      <el-form-item prop="username">
        <el-input
          v-model="form.username"
          placeholder="请输入用户名"
          prefix-icon="User"
        />
      </el-form-item>
      
      <el-form-item prop="password">
        <el-input
          v-model="form.password"
          type="password"
          placeholder="请输入密码"
          prefix-icon="Lock"
          show-password
        />
      </el-form-item>
      
      <el-form-item>
        <el-button
          type="primary"
          :loading="authStore.loginLoading"
          style="width: 100%"
          @click="handleLogin"
        >
          登录
        </el-button>
      </el-form-item>
    </el-form>
    
    <div class="login-footer">
      <el-link type="primary" @click="$router.push('/auth/register')">
        还没有账号？立即注册
      </el-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '@/store/auth'

const authStore = useAuthStore()
const formRef = ref<FormInstance>()

const form = reactive({
  username: 'admin',
  password: 'admin',
})

const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 4, message: '密码长度不能少于4位', trigger: 'blur' },
  ],
}

async function handleLogin() {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate()
  if (valid) {
    await authStore.authLogin(form)
  }
}
</script>

<style scoped>
.login-form {
  width: 100%;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
}
</style>
