<template>
  <div class="analytics-page">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>数据分析</span>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic
                title="总访问量"
                :value="statistics.totalViews"
                suffix="次"
              />
            </el-col>
            <el-col :span="8">
              <el-statistic
                title="今日访问"
                :value="statistics.todayViews"
                suffix="次"
              />
            </el-col>
            <el-col :span="8">
              <el-statistic
                title="活跃用户"
                :value="statistics.activeUsers"
                suffix="人"
              />
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>访问趋势</span>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              访问趋势图表
              <br>
              <small>（可集成 ECharts 或其他图表库）</small>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户分布</span>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              用户分布图表
              <br>
              <small>（可集成 ECharts 或其他图表库）</small>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>数据表格</span>
          </template>
          
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="views" label="访问量" width="120" />
            <el-table-column prop="users" label="用户数" width="120" />
            <el-table-column prop="bounce" label="跳出率" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

const statistics = reactive({
  totalViews: 125680,
  todayViews: 1234,
  activeUsers: 567,
})

const tableData = [
  {
    date: '2024-01-15',
    views: 1234,
    users: 567,
    bounce: '45.2%',
  },
  {
    date: '2024-01-14',
    views: 1156,
    users: 523,
    bounce: '42.8%',
  },
  {
    date: '2024-01-13',
    views: 1089,
    users: 498,
    bounce: '48.1%',
  },
  {
    date: '2024-01-12',
    views: 1345,
    users: 612,
    bounce: '41.5%',
  },
]
</script>

<style scoped>
.analytics-page {
  padding: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #999;
  font-size: 16px;
}
</style>
