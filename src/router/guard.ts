/**
 * 路由守卫配置
 */

import type { Router } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/user'
import { useAccessStore } from '@/store/access'
import { useAppStore } from '@/store/app'
import { LOGIN_PATH, DEFAULT_HOME_PATH } from '@/constants'
import { generateAccess } from './access'

/**
 * 创建路由守卫
 */
export function createRouterGuard(router: Router) {
  setupProgressGuard(router)
  setupAccessGuard(router)
  setupTitleGuard(router)
}

/**
 * 进度条守卫
 */
function setupProgressGuard(router: Router) {
  router.beforeEach(() => {
    const appStore = useAppStore()
    appStore.setLoading(true)
    return true
  })

  router.afterEach(() => {
    const appStore = useAppStore()
    appStore.setLoading(false)
  })
}

/**
 * 权限访问守卫
 */
function setupAccessGuard(router: Router) {
  router.beforeEach(async (to, from) => {
    const userStore = useUserStore()
    const accessStore = useAccessStore()

    // 如果是登录页面，直接放行
    if (to.path === LOGIN_PATH) {
      // 如果已经登录，重定向到首页
      if (userStore.isLoggedIn) {
        return { path: DEFAULT_HOME_PATH }
      }
      return true
    }

    // 如果是OAuth2回调页面，直接放行（允许未登录状态访问）
    if (to.path === '/oauth2/callback') {
      return true
    }

    // 检查是否已登录
    if (!userStore.isLoggedIn) {
      return {
        path: LOGIN_PATH,
        query: {
          redirect: to.fullPath,
        },
      }
    }

    // 检查是否已经检查过权限
    if (!accessStore.isAccessChecked) {
      try {
        // 生成访问权限
        await generateAccess({
          router,
          userStore,
          accessStore,
        })
        
        accessStore.setIsAccessChecked(true)
        
        // 重新导航到目标路由
        return { ...to, replace: true }
      } catch (error) {
        console.error('权限检查失败:', error)
        ElMessage.error('权限检查失败，请重新登录')
        
        // 清除登录状态
        userStore.clearUserInfo()
        accessStore.clearAccess()
        
        return {
          path: LOGIN_PATH,
          query: {
            redirect: to.fullPath,
          },
        }
      }
    }

    // 检查路由权限
    if (to.meta.roles && to.meta.roles.length > 0) {
      const hasRole = userStore.hasRole(to.meta.roles)
      if (!hasRole) {
        ElMessage.error('没有权限访问该页面')
        return { path: '/error/403' }
      }
    }

    if (to.meta.permissions && to.meta.permissions.length > 0) {
      const hasPermission = userStore.hasPermission(to.meta.permissions)
      if (!hasPermission) {
        ElMessage.error('没有权限访问该页面')
        return { path: '/error/403' }
      }
    }

    return true
  })
}

/**
 * 页面标题守卫
 */
function setupTitleGuard(router: Router) {
  router.afterEach((to) => {
    const appStore = useAppStore()
    const title = to.meta.title || '页面'
    const appTitle = appStore.preferences.app.title

    document.title = `${title} - ${appTitle}`
  })
}
