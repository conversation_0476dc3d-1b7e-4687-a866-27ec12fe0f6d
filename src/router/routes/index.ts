/**
 * 路由配置
 */

import type { RouteRecordRaw } from 'vue-router'
import { coreRoutes } from './core'
import { dynamicRoutes } from './dynamic'

// 基础路由（无需权限验证）
export const routes: RouteRecordRaw[] = [
  ...coreRoutes,
  // 404 路由必须放在最后
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hidden: true,
    },
  },
]

// 动态路由（需要权限验证）
export const accessRoutes: RouteRecordRaw[] = [...dynamicRoutes]

// 核心路由名称（用于重置路由时保留）
export const coreRouteNames = coreRoutes.map(route => route.name).filter(Boolean) as string[]
