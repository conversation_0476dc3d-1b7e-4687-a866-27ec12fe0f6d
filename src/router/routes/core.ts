/**
 * 核心路由配置
 */

import type { RouteRecordRaw } from 'vue-router'
import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@/constants'

/** 全局404页面 */
const fallbackNotFoundRoute: RouteRecordRaw = {
  path: '/:pathMatch(.*)*',
  name: 'FallbackNotFound',
  component: () => import('@/views/error/404.vue'),
  meta: {
    title: '404',
    hidden: true,
  },
}

/** 基本路由，这些路由是必须存在的 */
export const coreRoutes: RouteRecordRaw[] = [
  /**
   * 根路由
   * 使用基础布局，作为所有页面的父级容器
   */
  {
    path: '/',
    name: 'Root',
    component: () => import('@/layouts/BasicLayout.vue'),
    redirect: DEFAULT_HOME_PATH,
    meta: {
      title: 'Root',
      hidden: true,
    },
    children: [],
  },
  
  /**
   * 认证相关路由
   */
  {
    path: '/auth',
    name: 'Auth',
    component: () => import('@/layouts/AuthLayout.vue'),
    redirect: LOGIN_PATH,
    meta: {
      title: '认证',
      hidden: true,
    },
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/auth/login.vue'),
        meta: {
          title: '登录',
          hidden: true,
        },
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/auth/register.vue'),
        meta: {
          title: '注册',
          hidden: true,
        },
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: () => import('@/views/auth/forgot-password.vue'),
        meta: {
          title: '忘记密码',
          hidden: true,
        },
      },
    ],
  },

  /**
   * OAuth2 回调路由
   */
  {
    path: '/oauth2/callback',
    name: 'OAuth2Callback',
    component: () => import('@/views/auth/oauth2-callback.vue'),
    meta: {
      title: 'OAuth2 回调',
      hidden: true,
    },
  },

  /**
   * 错误页面路由
   */
  {
    path: '/error',
    name: 'Error',
    component: () => import('@/layouts/BasicLayout.vue'),
    meta: {
      title: '错误页面',
      hidden: true,
    },
    children: [
      {
        path: '403',
        name: 'Forbidden',
        component: () => import('@/views/error/403.vue'),
        meta: {
          title: '403 - 禁止访问',
          hidden: true,
        },
      },
      {
        path: '404',
        name: 'NotFound',
        component: () => import('@/views/error/404.vue'),
        meta: {
          title: '404 - 页面不存在',
          hidden: true,
        },
      },
      {
        path: '500',
        name: 'ServerError',
        component: () => import('@/views/error/500.vue'),
        meta: {
          title: '500 - 服务器错误',
          hidden: true,
        },
      },
    ],
  },
]

export { fallbackNotFoundRoute }
