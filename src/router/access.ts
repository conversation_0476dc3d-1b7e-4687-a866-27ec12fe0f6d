/**
 * 权限访问生成器
 */

import type { Router, RouteRecordRaw } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getAllMenusApi } from '@/api'
import type { MenuInfo } from '@/types/global'

interface GenerateAccessOptions {
  router: Router
  userStore: any
  accessStore: any
}

/**
 * 生成访问权限
 */
export async function generateAccess(options: GenerateAccessOptions) {
  const { router, userStore, accessStore } = options

  try {
    ElMessage({
      message: '正在加载菜单...',
      duration: 1500,
    })

    // 从后端获取菜单数据
    const menuData = await getAllMenusApi()

    // 将菜单数据转换为路由
    const routes = await generateRoutesFromMenus(menuData)

    // 生成菜单树
    const menus = generateMenuTree(menuData)

    // 添加路由到 router
    routes.forEach(route => {
      router.addRoute('Root', route)
    })

    // 存储到 store
    accessStore.setAccessRoutes(routes)
    accessStore.setAccessMenus(menus)

    return {
      routes,
      menus,
    }
  } catch (error) {
    console.error('生成访问权限失败:', error)

    // 返回空的访问权限，避免阻塞路由
    return {
      routes: [],
      menus: [],
    }
  }
}

/**
 * 根据菜单数据生成路由
 */
async function generateRoutesFromMenus(menus: MenuInfo[]): Promise<RouteRecordRaw[]> {
  const routes: RouteRecordRaw[] = []

  for (const menu of menus) {
    if (menu.type === 1 && menu.component) { // 只处理菜单类型且有组件的项
      const route: RouteRecordRaw = {
        path: menu.path,
        name: menu.name || menu.path.replace(/\//g, '_'),
        component: () => loadComponent(menu.component),
        meta: {
          title: menu.title,
          icon: menu.icon,
          hidden: menu.hidden,
          permissions: menu.permissions,
          roles: menu.roles,
        },
      }

      // 处理子路由
      if (menu.children && menu.children.length > 0) {
        route.children = await generateRoutesFromMenus(menu.children)
      }

      routes.push(route)
    }
  }

  return routes
}

/**
 * 动态加载组件
 */
function loadComponent(component: string) {
  // 组件路径映射
  const componentMap: Record<string, () => Promise<any>> = {
    // 仪表板
    'dashboard/index': () => import('@/views/dashboard/index.vue'),
    'dashboard/analytics': () => import('@/views/dashboard/analytics.vue'),

    // 系统管理
    'system/user/index': () => import('@/views/system/user/index.vue'),
    'system/role/index': () => import('@/views/system/role/index.vue'),
    'system/menu/index': () => import('@/views/system/menu/index.vue'),
    'system/dept/index': () => import('@/views/system/dept/index.vue'),

    // 个人中心
    'profile/index': () => import('@/views/profile/index.vue'),
  }

  return componentMap[component] || (() => import('@/views/error/404.vue'))
}

/**
 * 生成菜单树
 */
function generateMenuTree(menus: MenuInfo[]): MenuInfo[] {
  const menuTree: MenuInfo[] = []
  const menuMap = new Map<string, MenuInfo>()

  // 先将所有菜单放入 Map
  menus.forEach(menu => {
    menuMap.set(menu.id, { ...menu, children: [] })
  })

  // 构建树形结构
  menus.forEach(menu => {
    const menuItem = menuMap.get(menu.id)!

    if (menu.parentId && menuMap.has(menu.parentId)) {
      // 有父级，添加到父级的 children
      const parent = menuMap.get(menu.parentId)!
      if (!parent.children) {
        parent.children = []
      }
      parent.children.push(menuItem)
    } else {
      // 没有父级，是根菜单
      menuTree.push(menuItem)
    }
  })

  return menuTree
}

/**
 * 重置路由
 */
export function resetRoutes(router: Router) {
  // 获取所有路由
  const routes = router.getRoutes()
  
  // 移除动态添加的路由
  routes.forEach(route => {
    if (route.name && !isStaticRoute(route.name as string)) {
      router.removeRoute(route.name)
    }
  })
}

/**
 * 检查是否为静态路由
 */
function isStaticRoute(name: string): boolean {
  const staticRoutes = [
    'Root',
    'Auth',
    'Login',
    'Register', 
    'ForgotPassword',
    'OAuth2Callback',
    'Error',
    'Forbidden',
    'NotFound',
    'ServerError',
    'FallbackNotFound',
  ]
  
  return staticRoutes.includes(name)
}
