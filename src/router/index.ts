/**
 * Vue Router 配置
 */

import { createRouter, createWebHistory, createWebHashHistory } from 'vue-router'
import { routes } from './routes'
import { createRouterGuard } from './guard'

/**
 * 创建 Vue Router 实例
 */
const router = createRouter({
  history: import.meta.env.VITE_ROUTER_HISTORY === 'hash'
    ? createWebHashHistory(import.meta.env.VITE_BASE)
    : createWebHistory(import.meta.env.VITE_BASE),
  routes,
  scrollBehavior: (to, _from, savedPosition) => {
    if (savedPosition) {
      return savedPosition
    }
    return to.hash ? { behavior: 'smooth', el: to.hash } : { left: 0, top: 0 }
  },
})

// 路由错误处理
router.onError((error) => {
  console.error('路由导航错误:', error)
})

// 创建路由守卫
createRouterGuard(router)

export { router }
