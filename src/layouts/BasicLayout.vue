<template>
  <div class="basic-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="sidebar">
        <div class="logo">
          <h2>{{ appStore.preferences.app.title }}</h2>
        </div>
        <el-menu
          :default-active="$route.path"
          :collapse="appStore.sidebarCollapsed"
          router
        >
          <menu-item
            v-for="menu in accessStore.accessMenus"
            :key="menu.id"
            :menu="menu"
          />
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              :icon="Fold"
              @click="appStore.toggleSidebar"
              text
            />
          </div>
          <div class="header-right">
            <el-dropdown>
              <span class="user-info">
                <el-avatar :src="userStore.avatar" />
                <span>{{ userStore.realName || userStore.username }}</span>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="$router.push('/profile')">
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 内容区 -->
        <el-main class="main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Fold } from '@element-plus/icons-vue'
import { useAppStore } from '@/store/app'
import { useUserStore } from '@/store/user'
import { useAccessStore } from '@/store/access'
import { useAuthStore } from '@/store/auth'
import MenuItem from '@/components/MenuItem.vue'

const appStore = useAppStore()
const userStore = useUserStore()
const accessStore = useAccessStore()
const authStore = useAuthStore()

const sidebarWidth = computed(() => {
  return appStore.sidebarCollapsed ? '64px' : '240px'
})

async function handleLogout() {
  await authStore.logout()
}
</script>

<style scoped>
.basic-layout {
  height: 100vh;
}

.sidebar {
  background: #001529;
  transition: width 0.3s;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-bottom: 1px solid #1f1f1f;
}

.header {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.main {
  background: #f5f5f5;
  padding: 16px;
}
</style>
