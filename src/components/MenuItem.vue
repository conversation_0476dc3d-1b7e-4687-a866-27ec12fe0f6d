<template>
  <template v-if="!menu.hidden">
    <!-- 有子菜单 -->
    <el-sub-menu v-if="menu.children && menu.children.length > 0" :index="menu.path">
      <template #title>
        <el-icon v-if="menu.icon">
          <component :is="getIcon(menu.icon)" />
        </el-icon>
        <span>{{ menu.title }}</span>
      </template>
      <menu-item
        v-for="child in menu.children"
        :key="child.id"
        :menu="child"
      />
    </el-sub-menu>

    <!-- 无子菜单 -->
    <el-menu-item v-else :index="menu.path">
      <el-icon v-if="menu.icon">
        <component :is="getIcon(menu.icon)" />
      </el-icon>
      <template #title>{{ menu.title }}</template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import type { MenuInfo } from '@/types/global'
import { 
  Dashboard, 
  User, 
  Setting, 
  Menu as MenuIcon,
  Document,
  DataAnalysis,
  OfficeBuilding,
  UserFilled
} from '@element-plus/icons-vue'

interface Props {
  menu: MenuInfo
}

defineProps<Props>()

// 图标映射
const iconMap = {
  dashboard: Dashboard,
  user: User,
  system: Setting,
  menu: MenuIcon,
  role: UserFilled,
  dept: OfficeBuilding,
  chart: DataAnalysis,
  document: Document,
}

function getIcon(iconName: string) {
  return iconMap[iconName as keyof typeof iconMap] || Document
}
</script>
